import { test as base } from '@playwright/test';
import type { PlayWrightAiFixtureType } from '@midscene/web/playwright';
import { Assert } from '../utils/assertions';
import { MidsceneUtils } from '../utils/unified-midscene-utils';

// 在导入 PlaywrightAiFixture 前应用 Midscene 补丁
// 补丁已经在 unified-midscene-utils.ts 中自动应用

// 导入 PlaywrightAiFixture
import { PlaywrightAiFixture } from '@midscene/web/playwright';

// Define a new type that includes our custom assertion fixture
type MyFixtures = PlayWrightAiFixtureType & {
  assert: Assert;
};

// Extend the base test with both AI fixtures and our custom assertion fixture
export const test = base.extend<MyFixtures>({
  // Inherit the AI fixtures (已经通过补丁修改)
  ...PlaywrightAiFixture(),

  // Add our custom assert fixture
  assert: async ({ page, aiAssert }, use) => {
    await use(new Assert(page, aiAssert));
  },
});

// Windows 兼容性和缓存清理已通过 unified-midscene-utils.ts 和 npm scripts 自动处理
test.beforeAll(async () => {
  // 确保 Windows 兼容性设置已应用
  MidsceneUtils.setupWindowsCompatibility();
});