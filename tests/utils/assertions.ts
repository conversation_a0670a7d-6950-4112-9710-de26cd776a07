import { Page, expect } from '@playwright/test';

/**
 * Custom assertion class that extends <PERSON><PERSON>'s expect assertions.
 * Provides additional assertion methods for common test validations.
 */

export class Assert {
  constructor(private page: Page, private aiAssertFn?: any) {}

  /**
   * 断言页面上存在某个元素。
   * @param selector - 要检查的元素选择器。
   * @param errorMessage - 可选的错误提示信息。
   */
  async toBeVisible(selector: string, errorMessage?: string) {
    await expect(this.page.locator(selector), errorMessage).toBeVisible();
  }

  /**
   * 断言元素在页面上不可见或不存在。
   * @param selector - 要检查的元素选择器。
   * @param errorMessage - 可选的错误提示信息。
   */
  async toBeHidden(selector: string, errorMessage?: string) {
    await expect(this.page.locator(selector), errorMessage).toBeHidden();
  }

  /**
   * 断言元素包含预期的文本内容。
   * @param selector - 元素选择器。
   * @param expectedText - 期望元素包含的文本。
   * @param errorMessage - 可选的错误提示信息。
   */
  async toHaveText(selector: string, expectedText: string | RegExp, errorMessage?: string) {
    await expect(this.page.locator(selector), errorMessage).toHaveText(expectedText);
  }

  /**
   * 断言当前页面URL包含特定子字符串。
   * @param substring - URL中预期包含的子字符串。
   * @param errorMessage - 可选的错误提示信息。
   */
  async urlToContain(substring: string, errorMessage?: string) {
    await expect(this.page, errorMessage).toHaveURL(new RegExp(substring));
  }

  /**
   * 断言选择器匹配指定数量的元素。
   * @param selector - 要计数的元素选择器。
   * @param expectedCount - 期望匹配的元素数量。
   * @param errorMessage - 可选的错误提示信息。
   */
  async toHaveCount(selector: string, expectedCount: number, errorMessage?: string) {
    await expect(this.page.locator(selector), errorMessage).toHaveCount(expectedCount);
  }

  /**
   * Asserts that the actual string contains the expected substring.
   * @param actual - The actual string to check.
   * @param expected - The substring expected to be in the actual string.
   * @param errorMessage - Optional custom error message.
   */
  async toContain(actual: string, expected: string, errorMessage?: string) {
    if (!actual.includes(expected)) {
      throw new Error(errorMessage || `Expected string to contain "${expected}", but got "${actual}"`);
    }
  }

  /**
   * 断言元素具有指定的属性和值。
   * @param selector - 元素选择器。
   * @param attribute - 属性名称。
   * @param expectedValue - 属性的期望值。
   * @param errorMessage - 可选的错误提示信息。
   */
  async toHaveAttribute(selector: string, attribute: string, expectedValue: string | RegExp, errorMessage?: string) {
    await expect(this.page.locator(selector), errorMessage).toHaveAttribute(attribute, expectedValue);
  }

  /**
   * 断言值为真值（非null、非undefined、非false、非0、非空字符串等）。
   * @param value - 要检查的值。
   * @param errorMessage - 可选的错误提示信息。
   */
  isTruthy(value: any, errorMessage?: string) {
    expect(value, errorMessage).toBeTruthy();
  }

  /**
   * 使用AI进行断言，检查页面内容是否符合预期。
   * @param assertionDescription - 描述预期的断言内容，例如：'检查搜索结果列表第一条标题是否包含"playwright"字符串'。
   * @param options - 可选配置项。
   * @param options.timeout - 超时时间（毫秒），默认为10000毫秒。
   */
  async aiAssert(
    assertionDescription: string,
    options: { timeout?: number } = {}
  ): Promise<void> {
    const { timeout = 10000 } = options;

    try {
      // 如果有AI断言函数，使用它
      if (this.aiAssertFn) {
        await this.aiAssertFn(assertionDescription);
        return;
      }

      // 否则使用简化的验证逻辑
      await expect(async () => {
        // 基于页面内容的简单验证
        const pageContent = await this.page.textContent('body');

        // 根据断言描述进行基本检查
        if (assertionDescription.includes('新闻列表') && assertionDescription.includes('可见')) {
          // 检查页面是否包含新闻相关内容
          const hasNewsContent = pageContent && (
            pageContent.includes('新闻') ||
            pageContent.includes('资讯') ||
            pageContent.includes('最新') ||
            pageContent.includes('热点')
          );
          expect(hasNewsContent).toBe(true);
        } else if (assertionDescription.includes('标题文本')) {
          // 检查是否有标题元素
          const titleElements = await this.page.locator('h1, h2, h3, h4, h5, h6, .title, [class*="title"]').count();
          expect(titleElements).toBeGreaterThan(0);
        } else if (assertionDescription.includes('暂无数据') || assertionDescription.includes('加载失败')) {
          // 检查是否没有错误信息
          const hasErrorContent = pageContent && (
            pageContent.includes('暂无数据') ||
            pageContent.includes('加载失败') ||
            pageContent.includes('网络错误') ||
            pageContent.includes('服务器错误')
          );
          expect(hasErrorContent).toBe(false);
        } else {
          // 默认检查：页面内容不为空
          expect(pageContent && pageContent.trim().length > 0).toBe(true);
        }
      }).toPass({ timeout });
    } catch (error) {
      throw new Error(`AI断言失败: ${assertionDescription}\n${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
