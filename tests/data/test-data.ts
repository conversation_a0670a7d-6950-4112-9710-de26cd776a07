/**
 * 测试数据配置文件
 * 包含所有测试中使用的静态数据
 */

export const TestData = {
  // 用户认证信息
  auth: {
    phoneNumber: process.env.TEST_PHONE || '',
    password: process.env.TEST_PASSWORD || '',
    authFile: 'playwright/.auth/user.json'
  },

  // 超时配置
  timeouts: {
    login: 15000,
    networkIdle: 5000,
    default: 10000
  },

  // 测试消息
  messages: {
    errors: {
      timeEmpty: '获取到的时间不应为空',
      sourceEmpty: '获取到的来源不应为空',
      urlNavigation: 'URL should navigate to the expected page',
      newsListNotVisible: '首页新闻列表应该可见',
      newsListEmpty: '首页新闻列表不应为空',
      newsListTitlesEmpty: '新闻标题列表不应为空'
    },
    success: {
      loginSuccess: '登录成功',
      dataRetrieved: '数据获取成功'
    }
  },

  // AI 操作指令
  aiCommands: {
    navigation: {
      clickLogin: '点击"登录"',
      clickAccountLogin: '点击"账号密码登录"',
      clickOpinion: '点击"舆情"',
      clickHotNews: '点击"行业热点"',
      clickLatest: '点击"最新"',
      clickTimeSort: '点击"时间排序"',
      clickLoginButton: '点击"登录"按钮'
    },
    input: {
      enterPhone: '在"手机号"输入框中输入"***********"',
      enterPassword: '在"密码"输入框中输入"8131197.asd"'
    },
    query: {
      getFirstNewsTime: 'string, 获取列表里第一条新闻的发布时间，只返回时间信息',
      getFirstNewsSource: 'string, 获取列表里第一条新闻的来源，只返回来源信息',
      getHomeNewsListCount: 'number, 统计首页新闻列表中可见的新闻条目数量，只返回数字',
      getHomeNewsListTitles: 'array, 获取首页新闻列表中前10条新闻的标题，返回字符串数组格式',
      checkNewsListVisible: 'boolean, 检查首页是否存在新闻列表区域且内容正常显示'
    }
  }
};
