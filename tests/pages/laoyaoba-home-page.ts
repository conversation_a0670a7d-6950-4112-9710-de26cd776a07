import { Page } from '@playwright/test';
import { BasePage } from './base-page';
import { Assert } from '../utils/assertions';
import { URLs } from '../data/urls';
import { TestData } from '../data/test-data';

/**
 * laoyaoba首页页面对象
 */
export class LaoyaobaHomePage extends BasePage {
  constructor(page: Page, assert: Assert, ai: any, aiQuery: any) {
    super(page, assert, ai, aiQuery);
  }

  /**
   * 访问首页
   */
  async visit(): Promise<void> {
    console.log('🏠 开始访问laoyaoba首页...');

    // 添加重试机制
    const maxRetries = 3;
    let lastError: Error = new Error('未知错误');

    for (let i = 0; i < maxRetries; i++) {
      try {
        console.log(`🔄 尝试访问首页 (第${i + 1}次)`);
        await this.navigateToHome();

        // 验证页面是否正确加载
        const currentUrl = this.getCurrentUrl();
        const title = await this.page.title();

        console.log(`✅ 首页访问成功: ${currentUrl}`);
        console.log(`📋 页面标题: ${title}`);

        // 如果URL包含laoyaoba.com，认为访问成功
        if (currentUrl.includes('laoyaoba.com')) {
          return;
        } else {
          throw new Error(`页面URL不正确: ${currentUrl}`);
        }

      } catch (error) {
        lastError = error;
        console.warn(`⚠️ 第${i + 1}次访问失败:`, error.message);

        if (i < maxRetries - 1) {
          console.log('⏳ 等待2秒后重试...');
          await this.page.waitForTimeout(2000);
        }
      }
    }

    // 所有重试都失败了
    console.error('❌ 所有访问尝试都失败了');
    throw lastError;
  }

  /**
   * 执行登录操作
   */
  async login(): Promise<void> {
    // 批量执行登录相关AI操作
    await this.performAiActions([
      TestData.aiCommands.navigation.clickLogin,
      TestData.aiCommands.navigation.clickAccountLogin,
      TestData.aiCommands.input.enterPhone,
      TestData.aiCommands.input.enterPassword,
      TestData.aiCommands.navigation.clickLoginButton
    ]);
    // 等待登录成功，返回首页
    await this.page.waitForURL(URLs.base, { timeout: TestData.timeouts.login });
  }

  /**
   * 导航到舆情页面
   */
  async navigateToOpinion(): Promise<void> {
    await this.navigateByAiAction(
      TestData.aiCommands.navigation.clickOpinion,
      URLs.fragments.opinion,
      'URL should navigate to the opinion page'
    );
  }

  /**
   * 导航到最新新闻页面
   */
  async navigateToLatestNews(): Promise<void> {
    await this.navigateByAiAction(
      TestData.aiCommands.navigation.clickLatest,
      URLs.fragments.news,
      'URL should navigate to the news page'
    );
  }

  /**
   * 保存认证状态
   * @param authFilePath - 认证文件路径
   */
  async saveAuthState(authFilePath: string = TestData.auth.authFile): Promise<void> {
    await this.page.context().storageState({ path: authFilePath });
  }

  /**
   * 验证首页新闻列表是否正常显示
   */
  async verifyHomeNewsListDisplay(): Promise<void> {
    console.log('🔍 开始验证首页新闻列表显示...');

    // 首先检查页面基本状态
    const currentUrl = this.getCurrentUrl();
    const pageTitle = await this.page.title();
    console.log(`📋 页面状态检查 - URL: ${currentUrl}, 标题: ${pageTitle}`);

    // 检查页面内容是否加载
    const bodyContent = await this.page.textContent('body');
    if (!bodyContent || bodyContent.trim().length === 0) {
      throw new Error('页面内容为空，可能未正确加载');
    }
    console.log(`📋 页面内容长度: ${bodyContent.length} 字符`);

    // 等待页面加载完成
    await this.waitForPageLoad();

    try {
      // 检查新闻列表是否可见
      console.log('🤖 使用AI检查新闻列表可见性...');
      const isNewsListVisible = await this.performAiQuery(TestData.aiCommands.query.checkNewsListVisible);
      this.assert.isTruthy(isNewsListVisible, TestData.messages.errors.newsListNotVisible);
      console.log('✅ 新闻列表可见性验证通过');
    } catch (error) {
      console.error('❌ AI验证失败，尝试基础验证:', error);

      // 降级到基础验证
      const hasNewsKeywords = bodyContent.includes('新闻') ||
                             bodyContent.includes('资讯') ||
                             bodyContent.includes('最新') ||
                             bodyContent.includes('热点');

      if (!hasNewsKeywords) {
        throw new Error(`页面不包含新闻相关内容。页面URL: ${currentUrl}, 标题: ${pageTitle}`);
      }

      console.log('✅ 基础新闻内容验证通过');
    }
  }

  /**
   * 获取首页新闻列表数量
   * @returns 新闻条目数量
   */
  async getHomeNewsListCount(): Promise<number> {
    const countResult = await this.performAiQuery(TestData.aiCommands.query.getHomeNewsListCount);
    console.log(`AI查询新闻数量原始结果: "${countResult}"`);

    let newsCount = 0;

    // 尝试多种方式解析数量
    if (typeof countResult === 'number') {
      newsCount = countResult;
    } else if (typeof countResult === 'string') {
      // 提取字符串中的数字
      const numberMatch = countResult.match(/\d+/);
      if (numberMatch) {
        newsCount = parseInt(numberMatch[0]);
      }
    }

    console.log(`解析后的新闻数量: ${newsCount}`);
    this.assert.isTruthy(newsCount > 0, `${TestData.messages.errors.newsListEmpty}，AI返回: "${countResult}"`);
    return newsCount;
  }

  /**
   * 获取首页新闻列表标题
   * @returns 新闻标题数组
   */
  async getHomeNewsListTitles(): Promise<string[]> {
    const titlesResult = await this.performAiQuery(TestData.aiCommands.query.getHomeNewsListTitles);
    console.log(`AI查询新闻标题原始结果: "${titlesResult}"`);

    let titles: string[] = [];

    try {
      // 尝试解析为数组
      if (Array.isArray(titlesResult)) {
        titles = titlesResult;
      } else if (typeof titlesResult === 'string') {
        // 尝试JSON解析
        try {
          const parsed = JSON.parse(titlesResult);
          titles = Array.isArray(parsed) ? parsed : [parsed.toString()];
        } catch {
          // JSON解析失败，尝试按行分割或其他方式处理
          if (titlesResult.includes('\n')) {
            titles = titlesResult.split('\n').filter(title => title.trim().length > 0);
          } else if (titlesResult.includes('、')) {
            titles = titlesResult.split('、').filter(title => title.trim().length > 0);
          } else {
            titles = [titlesResult];
          }
        }
      } else {
        titles = [String(titlesResult)];
      }
    } catch (error) {
      console.log(`标题解析出错: ${error}`);
      titles = [titlesResult.toString()];
    }

    // 清理标题
    titles = titles.map(title => title.trim()).filter(title => title.length > 0);
    console.log(`解析后的新闻标题数量: ${titles.length}`);

    this.assert.isTruthy(titles.length > 0, `${TestData.messages.errors.newsListTitlesEmpty}，AI返回: "${titlesResult}"`);
    return titles;
  }

  /**
   * 验证首页新闻列表内容完整性
   * @returns 包含新闻数量和标题的对象
   */
  async validateHomeNewsListContent(): Promise<{ count: number; titles: string[] }> {
    // 验证新闻列表显示
    await this.verifyHomeNewsListDisplay();

    // 获取新闻数量
    const count = await this.getHomeNewsListCount();

    // 获取新闻标题
    const titles = await this.getHomeNewsListTitles();

    console.log(`首页新闻列表包含 ${count} 条新闻`);
    console.log(`新闻标题示例: ${titles.slice(0, 3).join(', ')}...`);

    return { count, titles };
  }
}
