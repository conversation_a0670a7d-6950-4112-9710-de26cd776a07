#!/usr/bin/env node

/**
 * 共享浏览器实现验证脚本
 * 验证laoyaoba-homepage-news-validation.spec.ts的共享浏览器配置
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 共享浏览器实现验证');
console.log('='.repeat(50));

try {
  // 检查测试文件是否存在
  const testFile = 'tests/laoyaoba/laoyaoba-homepage-news-validation.spec.ts';
  if (!fs.existsSync(testFile)) {
    console.log('❌ 测试文件不存在:', testFile);
    process.exit(1);
  }

  console.log('✅ 测试文件存在:', testFile);

  // 读取测试文件内容
  const content = fs.readFileSync(testFile, 'utf8');

  // 验证关键实现要素
  const checks = [
    {
      name: '串行测试配置',
      pattern: /test\.describe\.serial\(/,
      description: '使用test.describe.serial确保测试按顺序执行'
    },
    {
      name: '共享浏览器变量声明',
      pattern: /let\s+sharedBrowser:\s*Browser/,
      description: '声明共享的浏览器实例变量'
    },
    {
      name: '共享页面变量声明',
      pattern: /let\s+sharedPage:\s*Page/,
      description: '声明共享的页面实例变量'
    },
    {
      name: '共享上下文变量声明',
      pattern: /let\s+sharedContext:\s*BrowserContext/,
      description: '声明共享的浏览器上下文变量'
    },
    {
      name: 'beforeAll钩子',
      pattern: /test\.beforeAll\(/,
      description: '使用beforeAll钩子初始化共享资源'
    },
    {
      name: 'afterAll钩子',
      pattern: /test\.afterAll\(/,
      description: '使用afterAll钩子清理共享资源'
    },
    {
      name: '浏览器上下文创建',
      pattern: /sharedContext\s*=\s*await\s+sharedBrowser\.newContext/,
      description: '创建共享的浏览器上下文'
    },
    {
      name: '页面实例创建',
      pattern: /sharedPage\s*=\s*await\s+sharedContext\.newPage/,
      description: '创建共享的页面实例'
    },
    {
      name: '共享页面对象使用',
      pattern: /sharedHomePage/,
      description: '使用共享的页面对象'
    },
    {
      name: '资源清理',
      pattern: /await\s+sharedPage\.close\(\)/,
      description: '清理共享的页面资源'
    }
  ];

  console.log('\n🔍 验证共享浏览器实现要素:');
  let passedChecks = 0;

  checks.forEach((check, index) => {
    const found = check.pattern.test(content);
    const status = found ? '✅' : '❌';
    console.log(`${index + 1}. ${status} ${check.name}`);
    if (!found) {
      console.log(`   ⚠️  ${check.description}`);
    }
    if (found) passedChecks++;
  });

  // 统计结果
  const passRate = (passedChecks / checks.length * 100).toFixed(1);
  console.log('\n📊 验证结果:');
  console.log(`   通过: ${passedChecks}/${checks.length} (${passRate}%)`);

  if (passedChecks === checks.length) {
    console.log('✅ 共享浏览器实现验证通过！');
    
    console.log('\n🚀 预期执行效果:');
    console.log('   - 整个测试文件只启动1次浏览器');
    console.log('   - 3个测试用例共享同一个页面实例');
    console.log('   - 只访问1次首页，后续测试复用页面');
    console.log('   - 节省浏览器启动和页面加载时间');
    
    console.log('\n📋 测试执行顺序:');
    console.log('   1. beforeAll: 启动共享浏览器');
    console.log('   2. 测试1: 初始化页面对象 + 访问首页 + 验证显示');
    console.log('   3. 测试2: 复用页面对象 + 验证内容质量');
    console.log('   4. 测试3: 复用页面对象 + 验证视觉元素');
    console.log('   5. afterAll: 清理浏览器资源');
    
  } else {
    console.log('❌ 共享浏览器实现不完整');
    console.log('   请检查上述失败的验证项');
  }

  // 检查测试用例数量
  const testCases = content.match(/test\s*\(\s*['"`][^'"`]+['"`]/g) || [];
  console.log(`\n📈 发现测试用例数量: ${testCases.length}`);
  
  if (testCases.length === 3) {
    console.log('✅ 测试用例数量符合预期（3个）');
  } else {
    console.log('⚠️ 测试用例数量与预期不符（预期3个）');
  }

  // 提供运行建议
  console.log('\n🎯 运行建议:');
  console.log('   npm run test:laoyaoba  # 运行所有laoyaoba测试');
  console.log('   npm run test:homepage-news  # 单独运行首页验证测试');
  
} catch (error) {
  console.error('❌ 验证过程出错:', error.message);
  process.exit(1);
}

console.log('\n' + '='.repeat(50));
console.log('共享浏览器验证完成');
