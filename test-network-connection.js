#!/usr/bin/env node

/**
 * 网络连接和URL访问测试脚本
 * 验证是否能正常访问laoyaoba网站
 */

const { chromium } = require('@playwright/test');

async function testNetworkConnection() {
  console.log('🌐 网络连接测试');
  console.log('='.repeat(50));

  let browser;
  let context;
  let page;

  try {
    // 启动浏览器
    console.log('🚀 启动浏览器...');
    browser = await chromium.launch({ 
      headless: false,  // 显示浏览器窗口
      timeout: 30000 
    });
    
    // 创建上下文
    console.log('📋 创建浏览器上下文...');
    context = await browser.newContext({
      viewport: { width: 1280, height: 720 },
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    });
    
    // 创建页面
    console.log('📋 创建页面实例...');
    page = await context.newPage();
    
    console.log(`📋 初始页面URL: ${page.url()}`);
    
    // 测试访问laoyaoba首页
    const targetUrl = 'https://www.laoyaoba.com/';
    console.log(`🌐 尝试访问: ${targetUrl}`);
    
    const startTime = Date.now();
    const response = await page.goto(targetUrl, {
      waitUntil: 'networkidle',
      timeout: 30000
    });
    const loadTime = Date.now() - startTime;
    
    console.log(`⏱️  页面加载时间: ${loadTime}ms`);
    console.log(`📋 最终页面URL: ${page.url()}`);
    console.log(`📋 页面标题: ${await page.title()}`);
    
    if (response) {
      console.log(`📊 响应状态: ${response.status()} ${response.statusText()}`);
      console.log(`📊 响应URL: ${response.url()}`);
      
      if (response.ok()) {
        console.log('✅ 页面访问成功');
        
        // 检查页面内容
        const bodyText = await page.textContent('body');
        if (bodyText && bodyText.trim().length > 0) {
          console.log(`📄 页面内容长度: ${bodyText.length} 字符`);
          
          // 检查是否包含中文内容
          const hasChinese = /[\u4e00-\u9fa5]/.test(bodyText);
          console.log(`🈳 包含中文内容: ${hasChinese ? '是' : '否'}`);
          
          // 检查是否包含新闻相关内容
          const hasNewsContent = bodyText.includes('新闻') || bodyText.includes('资讯') || bodyText.includes('最新');
          console.log(`📰 包含新闻内容: ${hasNewsContent ? '是' : '否'}`);
          
        } else {
          console.log('⚠️ 页面内容为空');
        }
        
        // 等待几秒让用户看到页面
        console.log('⏳ 等待5秒以便查看页面...');
        await page.waitForTimeout(5000);
        
      } else {
        console.log(`❌ 页面访问失败: HTTP ${response.status()}`);
      }
    } else {
      console.log('❌ 没有收到响应');
    }
    
  } catch (error) {
    console.error('❌ 测试过程中出错:', error.message);
    
    if (page) {
      console.log(`📋 错误时页面URL: ${page.url()}`);
      try {
        const title = await page.title();
        console.log(`📋 错误时页面标题: ${title}`);
      } catch (e) {
        console.log('📋 无法获取页面标题');
      }
    }
    
  } finally {
    // 清理资源
    console.log('🧹 清理资源...');
    
    if (page) {
      await page.close();
    }
    
    if (context) {
      await context.close();
    }
    
    if (browser) {
      await browser.close();
    }
    
    console.log('✅ 资源清理完成');
  }
}

// 运行测试
if (require.main === module) {
  testNetworkConnection()
    .then(() => {
      console.log('\n' + '='.repeat(50));
      console.log('网络连接测试完成');
    })
    .catch((error) => {
      console.error('测试失败:', error);
      process.exit(1);
    });
}
