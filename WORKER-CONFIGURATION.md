# Laoyaoba测试动态Worker配置说明

## 🎯 配置目标

实现以下测试执行策略：
- **动态Worker数**: 根据laoyaoba目录下的测试文件数量自动适配
- **文件级并行**: 每个测试文件使用一个独立的worker
- **文件内串行**: 每个文件内的测试用例按顺序执行

## 📁 测试文件结构

```
tests/laoyaoba/
├── laoyaoba-news.spec.ts                   # 1个测试用例 → Worker 1
├── laoyaoba-homepage-news-validation.spec.ts # 3个测试用例 → Worker 2 (串行)
└── [future-test].spec.ts                   # 新测试文件 → Worker N (自动适配)
```

**动态适配示例**:
- 2个文件 → 2个worker
- 3个文件 → 3个worker
- N个文件 → N个worker

## ⚙️ 配置实现

### 1. 动态Worker计算
在 `playwright.laoyaoba.config.ts` 中实现动态worker计算：

```typescript
// 动态计算laoyaoba目录下的测试文件数量
function getTestFileCount(): number {
  const testDir = path.resolve(__dirname, 'tests/laoyaoba');
  const files = fs.readdirSync(testDir);
  const testFiles = files.filter(file => file.endsWith('.spec.ts'));
  return testFiles.length;
}

export default defineConfig({
  testDir: './tests/laoyaoba',
  fullyParallel: false,    // 文件内不并行
  workers: testFileCount,  // 动态设置worker数量
  // ... 其他配置
});
```

### 2. 共享浏览器实例配置
在 `laoyaoba-homepage-news-validation.spec.ts` 中实现浏览器实例共享：

```typescript
test.describe.serial('laoyaoba-homepage-news-validation', () => {
  // 共享的浏览器实例和页面对象
  let sharedBrowser: Browser;
  let sharedContext: BrowserContext;
  let sharedPage: Page;
  let sharedHomePage: LaoyaobaHomePage;

  // 在所有测试开始前启动一次浏览器
  test.beforeAll(async ({ browser }) => {
    sharedBrowser = browser;
    sharedContext = await sharedBrowser.newContext();
    sharedPage = await sharedContext.newPage();
  });

  // 3个测试用例共享同一个浏览器实例
  test('validate-homepage-news-list-display', ...);  // 初始化+测试
  test('validate-homepage-news-list-content-quality', ...);  // 复用页面
  test('validate-homepage-news-list-visual-elements', ...);  // 复用页面
});
```

### 3. 动态Worker运行器
创建了 `scripts/dynamic-worker-runner.js` 专门处理动态worker：

```javascript
// 动态计算测试文件数量
function getTestFileCount() {
  const testDir = path.resolve(__dirname, '../tests/laoyaoba');
  const files = fs.readdirSync(testDir);
  const testFiles = files.filter(file => file.endsWith('.spec.ts'));
  return { count: testFiles.length, files: testFiles };
}

// 显示worker配置信息
console.log(`📁 测试文件数量: ${count}`);
console.log(`⚙️  将使用 ${count} 个worker（每个文件一个worker）`);
```

## 🚀 运行方式

### 推荐方式（动态Worker配置）
```bash
npm run test:laoyaoba
```
**执行效果**:
- 自动检测测试文件数量
- 每个测试文件使用一个独立worker
- 显示详细的worker分配信息

### 备用方式
```bash
# 使用默认配置
npm run test:laoyaoba-basic

# 使用固定配置（如果需要）
npm run test:laoyaoba-fixed
```

### 直接使用Playwright
```bash
# 使用专用配置
npx playwright test --headed --config playwright.laoyaoba.config.ts

# 使用默认配置
npx playwright test --headed tests/laoyaoba/
```

## 📊 预期执行结果

当您运行 `npm run test:laoyaoba` 时，首先会看到动态配置信息：

```
🚀 动态Worker测试运行器
==================================================
📁 测试文件数量: 2
📋 测试文件列表: laoyaoba-news.spec.ts, laoyaoba-homepage-news-validation.spec.ts
⚙️  将使用 2 个worker（每个文件一个worker）
==================================================
```

然后是测试执行：

```
Running 4 tests using 2 workers

  1 [chromium] › laoyaoba-news.spec.ts:11:7 › laoyaoba-news › get-latest-news
  2 [chromium] › laoyaoba-homepage-news-validation.spec.ts:10:7 › validate-homepage-news-list-display
  3 [chromium] › laoyaoba-homepage-news-validation.spec.ts:48:7 › validate-homepage-news-list-content-quality
  4 [chromium] › laoyaoba-homepage-news-validation.spec.ts:97:7 › validate-homepage-news-list-visual-elements
```

**动态执行策略**:
- 自动检测到2个测试文件 → 使用2个worker
- Worker 1: 执行 `laoyaoba-news.spec.ts` 中的1个测试（独立浏览器）
- Worker 2: 执行 `laoyaoba-homepage-news-validation.spec.ts` 中的3个测试（共享浏览器）
  - 🚀 启动1次浏览器
  - 📋 测试1: 初始化+验证显示
  - 📋 测试2: 复用页面+验证质量
  - 📋 测试3: 复用页面+验证元素
  - 🧹 清理浏览器资源

## 🔧 配置优势

1. **智能资源分配**: 动态worker数量，充分利用系统资源
2. **浏览器实例优化**: 共享浏览器实例，减少启动开销
3. **执行效率提升**:
   - 文件级并行提高整体速度
   - 浏览器共享减少重复启动（节省10-20秒）
   - 页面复用减少重复加载（节省5-10秒）
4. **状态管理**: 串行执行确保测试状态一致性
5. **资源清理**: 自动管理浏览器生命周期

## 🛠️ 故障排除

如果worker数量不符合预期：

1. **检查配置文件**: 确认使用了正确的配置文件
2. **查看日志**: 观察Playwright输出的worker信息
3. **系统资源**: 确保系统有足够资源支持2个worker
4. **CI环境**: CI环境可能会覆盖worker设置

## 📝 后续扩展

当添加新的测试文件时：
- 新文件会自动分配到可用的worker
- 如需特殊执行顺序，可使用 `test.describe.serial()`
- 可根据需要调整worker数量
