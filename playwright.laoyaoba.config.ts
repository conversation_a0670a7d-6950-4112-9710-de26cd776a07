import { defineConfig, devices } from '@playwright/test';
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';

// Load environment variables from .env file
dotenv.config({ path: path.resolve(__dirname, '.env') });

// 动态计算laoyaoba目录下的测试文件数量
function getTestFileCount(): number {
  try {
    const testDir = path.resolve(__dirname, 'tests/laoyaoba');
    const files = fs.readdirSync(testDir);
    const testFiles = files.filter(file => file.endsWith('.spec.ts'));
    const count = testFiles.length;
    console.log(`🔍 发现 ${count} 个测试文件: ${testFiles.join(', ')}`);
    return count;
  } catch (error) {
    console.warn('⚠️ 无法计算测试文件数量，使用默认值 1');
    return 1;
  }
}

const testFileCount = getTestFileCount();

/**
 * Playwright configuration specifically for laoyaoba tests
 * 动态适配worker数量：根据测试文件数量自动调整
 */
export default defineConfig({
  testDir: './tests/laoyaoba',
  /* Timeout settings */
  timeout: 90 * 1000,
  /* Run test files in parallel, but tests within each file serially */
  fullyParallel: false,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 0,
  /* 动态设置worker数量：每个测试文件一个worker */
  workers: process.env.CI ? 1 : testFileCount,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [
    ["list"],
    [
      "@midscene/web/playwright-report",
      {
        // 配置 Midscene 报告器以处理 Windows 文件名兼容性
        cacheDir: process.env.MIDSCENE_CACHE_DIR || 'midscene_run/cache',
        safeFileNames: process.env.MIDSCENE_SAFE_FILE_NAMES === 'true'
      }
    ]
  ],
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on-first-retry',
  },

  /* Configure projects for major browsers */
  projects: [
    {
      name: 'chromium',
      use: {
        ...devices['Desktop Chrome'],
        // Use prepared auth state if available
        storageState: 'playwright/.auth/user.json',
      },
      // Exclude setup files from this configuration
      testIgnore: /.*\.setup\.ts/,
    },
  ],
});
