#!/usr/bin/env node

/**
 * 动态Worker配置验证脚本
 * 验证laoyaoba测试的动态worker配置是否正确
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 动态Worker配置验证');
console.log('='.repeat(50));

try {
  console.log('📋 检查配置文件...');

  // 检查专用配置文件是否存在
  if (fs.existsSync('playwright.laoyaoba.config.ts')) {
    console.log('✅ playwright.laoyaoba.config.ts 存在');
  } else {
    console.log('❌ playwright.laoyaoba.config.ts 不存在');
    process.exit(1);
  }

  // 动态检查测试文件
  console.log('\n🔍 动态扫描测试文件...');
  const testDir = path.resolve(__dirname, 'tests/laoyaoba');
  const files = fs.readdirSync(testDir);
  const testFiles = files.filter(file => file.endsWith('.spec.ts'));

  console.log(`📁 发现 ${testFiles.length} 个测试文件:`);
  testFiles.forEach(file => {
    console.log(`   ✅ ${file}`);
  });

  console.log(`⚙️  预期worker数量: ${testFiles.length}`);
  
  console.log('\n🔍 运行配置测试（dry-run）...');
  
  // 运行dry-run来检查配置
  const result = execSync('npx playwright test --config playwright.laoyaoba.config.ts --list', {
    encoding: 'utf8',
    stdio: 'pipe'
  });
  
  console.log('📊 测试发现结果:');
  console.log(result);
  
  // 检查是否发现了预期的测试数量
  const discoveredTestCount = (result.match(/\.spec\.ts/g) || []).length;
  console.log(`\n📈 Playwright发现的测试文件数量: ${discoveredTestCount}`);
  console.log(`🎯 预期worker数量: ${testFiles.length}`);

  if (discoveredTestCount === testFiles.length) {
    console.log('✅ 动态Worker配置验证通过！');
    console.log('\n🚀 可以运行以下命令开始测试:');
    console.log('   npm run test:laoyaoba  (动态worker)');
    console.log('   npm run test:laoyaoba-basic  (默认配置)');
    console.log('\n📊 执行效果:');
    console.log(`   - 将使用 ${testFiles.length} 个worker`);
    console.log('   - 每个测试文件使用一个独立的worker');
    console.log('   - 文件内的测试用例按顺序执行');
  } else {
    console.log('⚠️ 发现的测试文件数量与预期不符');
    console.log(`   预期: ${testFiles.length}, 实际: ${discoveredTestCount}`);
  }
  
} catch (error) {
  console.error('❌ 配置验证失败:', error.message);
  process.exit(1);
}

console.log('\n' + '='.repeat(50));
console.log('配置验证完成');
