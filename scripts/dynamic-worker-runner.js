#!/usr/bin/env node

/**
 * 动态Worker运行器
 * 根据laoyaoba目录下的测试文件数量动态设置worker数量
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * 获取测试文件数量
 */
function getTestFileCount() {
  try {
    const testDir = path.resolve(__dirname, '../tests/laoyaoba');
    const files = fs.readdirSync(testDir);
    const testFiles = files.filter(file => file.endsWith('.spec.ts'));
    return { count: testFiles.length, files: testFiles };
  } catch (error) {
    console.warn('⚠️ 无法读取测试目录，使用默认值');
    return { count: 1, files: [] };
  }
}

/**
 * 设置环境变量
 */
function setupEnvironment() {
  // 设置 Windows 兼容性环境变量
  process.env.MIDSCENE_SAFE_FILE_NAMES = 'true';
  
  // 确保缓存目录路径正确
  if (process.platform === 'win32') {
    process.env.MIDSCENE_CACHE_DIR = 'midscene_run\\cache';
  } else {
    process.env.MIDSCENE_CACHE_DIR = 'midscene_run/cache';
  }
}

/**
 * 清理缓存文件
 */
function cleanupCacheFiles(dirPath) {
  try {
    const { cleanDirectory } = require('./utils/windows-utils');
    console.log(`开始清理缓存目录: ${dirPath}`);
    const cleanedCount = cleanDirectory(dirPath);
    if (cleanedCount > 0) {
      console.log(`清理完成，共处理了 ${cleanedCount} 个项目`);
    } else {
      console.log('没有发现需要清理的项目');
    }
  } catch (error) {
    console.log('缓存清理跳过（工具不可用）');
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 动态Worker测试运行器');
  console.log('='.repeat(50));
  
  // 获取测试文件信息
  const { count, files } = getTestFileCount();
  
  console.log(`📁 测试文件数量: ${count}`);
  console.log(`📋 测试文件列表: ${files.join(', ')}`);
  console.log(`⚙️  将使用 ${count} 个worker（每个文件一个worker）`);
  console.log('='.repeat(50));
  
  // 设置环境变量
  setupEnvironment();
  
  // 清理缓存文件
  const cacheDir = process.env.MIDSCENE_CACHE_DIR || 'midscene_run/cache';
  cleanupCacheFiles(cacheDir);
  
  // 获取命令行参数
  const args = process.argv.slice(2);
  
  // 检查是否有配置文件参数
  let configFile = '';
  let testCommand = '';
  
  args.forEach(arg => {
    if (arg.startsWith('--config=')) {
      configFile = ` --config ${arg.split('=')[1]}`;
    } else {
      testCommand += arg + ' ';
    }
  });
  
  // 默认使用laoyaoba专用配置
  if (!configFile) {
    configFile = ' --config playwright.laoyaoba.config.ts';
  }
  
  const fullCommand = `npx playwright test --headed${configFile} ${testCommand.trim()}`;
  console.log(`🎯 执行命令: ${fullCommand}`);
  console.log('='.repeat(50));
  
  try {
    // 运行测试
    execSync(fullCommand, {
      stdio: 'inherit',
      env: { ...process.env }
    });

    console.log('✅ 测试运行完成');

    // 测试完成后再次清理缓存文件
    console.log('🧹 清理测试生成的缓存文件...');
    cleanupCacheFiles(cacheDir);

  } catch (error) {
    console.error('❌ 测试运行失败:', error.message);

    // 即使测试失败也要清理缓存文件
    console.log('🧹 清理缓存文件...');
    cleanupCacheFiles(cacheDir);

    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}
