#!/usr/bin/env node

/**
 * Windows 兼容性测试运行脚本
 * 处理文件名兼容性问题并运行测试
 */

const { cleanDirectory } = require('./utils/windows-utils');
const { execSync } = require('child_process');
const path = require('path');

/**
 * 清理缓存目录中的问题文件
 * @param {string} dirPath - 目录路径
 */
function cleanupCacheFiles(dirPath) {
  console.log(`开始清理缓存目录: ${dirPath}`);
  const cleanedCount = cleanDirectory(dirPath);
  if (cleanedCount > 0) {
    console.log(`清理完成，共处理了 ${cleanedCount} 个项目`);
  } else {
    console.log('没有发现需要清理的项目');
  }
}

/**
 * 设置环境变量
 */
function setupEnvironment() {
  // 设置 Windows 兼容性环境变量
  process.env.MIDSCENE_SAFE_FILE_NAMES = 'true';
  
  // 确保缓存目录路径正确
  if (process.platform === 'win32') {
    process.env.MIDSCENE_CACHE_DIR = 'midscene_run\\cache';
  } else {
    process.env.MIDSCENE_CACHE_DIR = 'midscene_run/cache';
  }
  
  console.log('环境变量设置完成');
  console.log(`MIDSCENE_SAFE_FILE_NAMES: ${process.env.MIDSCENE_SAFE_FILE_NAMES}`);
  console.log(`MIDSCENE_CACHE_DIR: ${process.env.MIDSCENE_CACHE_DIR}`);
}

/**
 * 主函数
 */
function main() {
  console.log('Windows 兼容性测试运行器');
  console.log('========================');
  
  // 设置环境变量
  setupEnvironment();
  
  // 清理缓存文件
  const cacheDir = process.env.MIDSCENE_CACHE_DIR || 'midscene_run/cache';
  cleanupCacheFiles(cacheDir);
  
  // 获取命令行参数
  const args = process.argv.slice(2);

  // 检查是否有配置文件参数
  let configFile = '';
  let testCommand = '';

  args.forEach(arg => {
    if (arg.startsWith('--config=')) {
      configFile = ` --config ${arg.split('=')[1]}`;
    } else {
      testCommand += arg + ' ';
    }
  });

  // 如果没有指定测试命令，使用默认值
  if (!testCommand.trim()) {
    testCommand = 'tests/laoyaoba/laoyaoba-news.spec.ts';
  }

  const fullCommand = `npx playwright test --headed${configFile} ${testCommand.trim()}`;
  console.log(`运行测试: ${fullCommand}`);
  console.log('========================');

  try {
    // 运行测试
    execSync(fullCommand, {
      stdio: 'inherit',
      env: { ...process.env }
    });

    console.log('测试运行完成');

    // 测试完成后再次清理缓存文件
    console.log('清理测试生成的缓存文件...');
    cleanupCacheFiles(cacheDir);

  } catch (error) {
    console.error('测试运行失败:', error.message);

    // 即使测试失败也要清理缓存文件
    console.log('清理缓存文件...');
    cleanupCacheFiles(cacheDir);

    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}
