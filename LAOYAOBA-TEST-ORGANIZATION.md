# Laoyaoba测试组织说明

## 📁 测试文件结构

```
tests/laoyaoba/
├── auth.setup.ts                           # 认证设置文件
├── laoyaoba-news.spec.ts                   # 新闻数据获取测试
└── laoyaoba-homepage-news-validation.spec.ts # 首页新闻列表验证测试
```

## 🚀 运行方式

### 运行所有laoyaoba测试（推荐）
```bash
npm run test:laoyaoba
```
**说明**: 这个命令会运行 `tests/laoyaoba/` 目录下的所有测试文件，包括：
- `laoyaoba-news.spec.ts` - 新闻数据获取测试
- `laoyaoba-homepage-news-validation.spec.ts` - 首页新闻列表验证测试
- 以及将来添加到该目录的任何新测试文件

### 单独运行首页验证测试
```bash
npm run test:homepage-news
```
**说明**: 只运行首页新闻列表验证测试

### 直接使用Playwright
```bash
# 运行整个laoyaoba目录的测试
npx playwright test --headed tests/laoyaoba/

# 运行单个测试文件
npx playwright test --headed tests/laoyaoba/laoyaoba-news.spec.ts
npx playwright test --headed tests/laoyaoba/laoyaoba-homepage-news-validation.spec.ts
```

## 🔄 自动扩展

当你在 `tests/laoyaoba/` 目录下添加新的测试文件时（如 `laoyaoba-xxx.spec.ts`），运行 `npm run test:laoyaoba` 会自动包含这些新测试，无需修改任何配置。

## 📊 测试覆盖范围

### 现有测试
1. **新闻数据获取测试** (`laoyaoba-news.spec.ts`)
   - 验证最新新闻数据获取功能
   - 检查时间和来源格式

2. **首页新闻列表验证测试** (`laoyaoba-homepage-news-validation.spec.ts`)
   - 验证首页新闻列表显示正常
   - 检查新闻数量和标题质量
   - 使用AI断言验证视觉元素

### 未来扩展建议
- 搜索功能测试
- 用户登录流程测试
- 页面导航测试
- 响应式布局测试

## 💡 最佳实践

1. **命名规范**: 新测试文件请使用 `laoyaoba-[功能名称].spec.ts` 格式
2. **测试分组**: 相关功能的测试用例放在同一个 `.spec.ts` 文件中
3. **页面对象**: 充分利用 `LaoyaobaHomePage` 等页面对象类
4. **AI命令**: 在 `tests/data/test-data.ts` 中统一管理AI命令

## 🔧 技术细节

- 使用 `scripts/run-tests-windows.js` 脚本确保Windows兼容性
- 自动清理Midscene缓存文件
- 支持AI驱动的元素识别和断言
- 集成详细的测试报告和日志
